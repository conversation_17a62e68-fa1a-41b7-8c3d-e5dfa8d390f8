import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:vibration/vibration.dart';

/// Enhanced haptic feedback service with multiple fallback methods
class HapticService {
  static final HapticService _instance = HapticService._internal();
  factory HapticService() => _instance;
  HapticService._internal();

  bool _hapticEnabled = true;
  bool _vibrationSupported = false;
  bool _initialized = false;

  /// Initialize the haptic service and check device capabilities
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      if (kDebugMode) {
        print('HapticService: Initializing...');
      }

      // Check if vibration is supported
      final hasVibrator = await Vibration.hasVibrator();
      _vibrationSupported = hasVibrator ?? false;

      if (kDebugMode) {
        print('HapticService: Vibration supported: $_vibrationSupported');
      }

      _initialized = true;
    } catch (e) {
      if (kDebugMode) {
        print('HapticService: Initialization failed: $e');
      }
      _vibrationSupported = false;
      _initialized = true;
    }
  }

  /// Perform light haptic feedback for navigation and UI interactions
  Future<void> lightImpact() async {
    if (!_hapticEnabled) {
      if (kDebugMode) {
        print('HapticService: Haptic feedback is disabled');
      }
      return;
    }

    await initialize();

    if (kDebugMode) {
      print('HapticService: Attempting light impact feedback...');
    }

    bool success = false;

    // Method 1: Try Flutter's HapticFeedback.lightImpact()
    try {
      await HapticFeedback.lightImpact();
      success = true;
      if (kDebugMode) {
        print('HapticService: ✅ Flutter lightImpact() succeeded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('HapticService: ❌ Flutter lightImpact() failed: $e');
      }
    }

    // Method 2: Try Flutter's HapticFeedback.selectionClick() as fallback
    if (!success) {
      try {
        await HapticFeedback.selectionClick();
        success = true;
        if (kDebugMode) {
          print('HapticService: ✅ Flutter selectionClick() succeeded');
        }
      } catch (e) {
        if (kDebugMode) {
          print('HapticService: ❌ Flutter selectionClick() failed: $e');
        }
      }
    }

    // Method 3: Try vibration package with short vibration
    if (!success && _vibrationSupported) {
      try {
        await Vibration.vibrate(duration: 50); // Short 50ms vibration
        success = true;
        if (kDebugMode) {
          print('HapticService: ✅ Vibration package (50ms) succeeded');
        }
      } catch (e) {
        if (kDebugMode) {
          print('HapticService: ❌ Vibration package failed: $e');
        }
      }
    }

    // Method 4: Try different Flutter haptic types
    if (!success) {
      try {
        await HapticFeedback.mediumImpact();
        success = true;
        if (kDebugMode) {
          print('HapticService: ✅ Flutter mediumImpact() succeeded');
        }
      } catch (e) {
        if (kDebugMode) {
          print('HapticService: ❌ Flutter mediumImpact() failed: $e');
        }
      }
    }

    if (!success) {
      if (kDebugMode) {
        print('HapticService: ❌ All haptic feedback methods failed');
      }
    }
  }

  /// Perform medium haptic feedback for button presses
  Future<void> mediumImpact() async {
    if (!_hapticEnabled) return;

    await initialize();

    if (kDebugMode) {
      print('HapticService: Attempting medium impact feedback...');
    }

    try {
      await HapticFeedback.mediumImpact();
      if (kDebugMode) {
        print('HapticService: ✅ Medium impact succeeded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('HapticService: ❌ Medium impact failed, trying alternatives: $e');
      }
      // Fallback to light impact
      await lightImpact();
    }
  }

  /// Perform heavy haptic feedback for important actions
  Future<void> heavyImpact() async {
    if (!_hapticEnabled) return;

    await initialize();

    if (kDebugMode) {
      print('HapticService: Attempting heavy impact feedback...');
    }

    try {
      await HapticFeedback.heavyImpact();
      if (kDebugMode) {
        print('HapticService: ✅ Heavy impact succeeded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('HapticService: ❌ Heavy impact failed, trying vibration: $e');
      }
      // Fallback to longer vibration
      if (_vibrationSupported) {
        try {
          await Vibration.vibrate(duration: 100);
          if (kDebugMode) {
            print('HapticService: ✅ Heavy vibration (100ms) succeeded');
          }
        } catch (e2) {
          await lightImpact(); // Final fallback
        }
      } else {
        await lightImpact(); // Final fallback
      }
    }
  }

  /// Test all haptic feedback methods
  Future<void> testAllMethods() async {
    if (kDebugMode) {
      print('HapticService: 🧪 Testing all haptic feedback methods...');
    }

    await initialize();

    // Test 1: Light impact
    if (kDebugMode) {
      print('HapticService: Test 1 - Light Impact');
    }
    await lightImpact();
    await Future.delayed(const Duration(milliseconds: 500));

    // Test 2: Medium impact
    if (kDebugMode) {
      print('HapticService: Test 2 - Medium Impact');
    }
    await mediumImpact();
    await Future.delayed(const Duration(milliseconds: 500));

    // Test 3: Heavy impact
    if (kDebugMode) {
      print('HapticService: Test 3 - Heavy Impact');
    }
    await heavyImpact();

    if (kDebugMode) {
      print('HapticService: 🧪 All tests completed');
    }
  }

  /// Enable or disable haptic feedback
  void setHapticEnabled(bool enabled) {
    _hapticEnabled = enabled;
    if (kDebugMode) {
      print('HapticService: Haptic feedback enabled: $enabled');
    }
  }

  /// Check if haptic feedback is enabled
  bool get isHapticEnabled => _hapticEnabled;

  /// Check if vibration is supported on this device
  bool get isVibrationSupported => _vibrationSupported;

  /// Get device capabilities info
  Map<String, dynamic> getDeviceInfo() {
    return {
      'hapticEnabled': _hapticEnabled,
      'vibrationSupported': _vibrationSupported,
      'initialized': _initialized,
    };
  }
}
