import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_sixvalley_ecommerce/common/services/sound_service.dart';
import 'package:flutter_sixvalley_ecommerce/common/services/haptic_service.dart';
import 'package:flutter_sixvalley_ecommerce/utill/dimensions.dart';

/// Debug widget to test sound and haptic feedback functionality
/// Only visible in debug mode
class DebugTestWidget extends StatelessWidget {
  const DebugTestWidget({super.key});

  Future<Map<String, dynamic>> _getDeviceInfo() async {
    await HapticService().initialize();
    return HapticService().getDeviceInfo();
  }

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(Dimensions.paddingSizeSmall),
      padding: const EdgeInsets.all(Dimensions.paddingSizeSmall),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        border: Border.all(color: Colors.red),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'DEBUG: Test Sound & Haptic',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () async {
                  print('DEBUG: Testing sound...');
                  await SoundService().testSound();
                },
                child: const Text('Test Sound'),
              ),
              ElevatedButton(
                onPressed: () async {
                  print('DEBUG: Testing light haptic...');
                  await HapticService().lightImpact();
                },
                child: const Text('Light Haptic'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () async {
                  print('DEBUG: Testing medium haptic...');
                  await HapticService().mediumImpact();
                },
                child: const Text('Medium Haptic'),
              ),
              ElevatedButton(
                onPressed: () async {
                  print('DEBUG: Testing all haptic methods...');
                  await HapticService().testAllMethods();
                },
                child: const Text('Test All'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          FutureBuilder<Map<String, dynamic>>(
            future: _getDeviceInfo(),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                final info = snapshot.data!;
                return Text(
                  'Vibration: ${info['vibrationSupported']}\nHaptic: ${info['hapticEnabled']}',
                  style: const TextStyle(fontSize: 10, color: Colors.red),
                  textAlign: TextAlign.center,
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }
}
