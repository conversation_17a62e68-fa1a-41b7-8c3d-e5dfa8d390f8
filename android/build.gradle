// build.gradle (allprojects)

// Set up repositories for dependencies
allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // Set the build directory
    rootProject.buildDir = '../build'

    // Configure subprojects
    subprojects {
        project.buildDir = "${rootProject.buildDir}/${project.name}"
    }

    subprojects {
        afterEvaluate { project ->
            if (project.hasProperty('android')) {
                project.android {
                    if (namespace == null) {
                        namespace project.group
                    }


                    compileOptions {
                        sourceCompatibility JavaVersion.VERSION_17
                        targetCompatibility JavaVersion.VERSION_17
                    }
                    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                        kotlinOptions {
                            jvmTarget = "17"
                        }
                    }
                    java {
                        toolchain {
                            languageVersion = JavaLanguageVersion.of(17)
                        }
                    }


                }
            }
        }
    }

    // Ensure evaluation depends on the app module
    subprojects {
        project.evaluationDependsOn(':app')
    }


}