{"buildFiles": ["/Users/<USER>/fvm/versions/stable/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/user-jaagur/android/app/.cxx/RelWithDebInfo/375n2k4s/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/user-jaagur/android/app/.cxx/RelWithDebInfo/375n2k4s/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}