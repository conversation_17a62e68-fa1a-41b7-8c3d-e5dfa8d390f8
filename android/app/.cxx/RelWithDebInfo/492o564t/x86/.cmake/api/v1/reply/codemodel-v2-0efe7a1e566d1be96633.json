{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/6amTech/gitHub/6velly/sixvalley-user-app/android/app/.cxx/RelWithDebInfo/492o564t/x86", "source": "/Users/<USER>/Documents/development/all/latest/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}