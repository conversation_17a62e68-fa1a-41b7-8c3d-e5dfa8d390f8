{"buildFiles": ["/Users/<USER>/Documents/development/all/latest/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/6amTech/gitHub/6velly/sixvalley-user-app/android/app/.cxx/RelWithDebInfo/492o564t/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/6amTech/gitHub/6velly/sixvalley-user-app/android/app/.cxx/RelWithDebInfo/492o564t/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}